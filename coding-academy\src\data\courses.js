export const courses = [
  {
    id: 1,
    title: "أساسيات JavaScript",
    description: "تعلم أساسيات لغة JavaScript من الصفر حتى الاحتراف",
    instructor: "أحمد محمد",
    duration: "8 أسابيع",
    level: "مبتدئ",
    price: 299,
    rating: 4.8,
    students: 1250,
    image: "https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=400",
    category: "Frontend",
    tags: ["JavaScript", "Programming", "Web Development"],
    lessons: [
      { id: 1, title: "مقدمة في JavaScript", duration: "45 دقيقة", completed: false },
      { id: 2, title: "المتغيرات والأنواع", duration: "60 دقيقة", completed: false },
      { id: 3, title: "الدوال والكائنات", duration: "75 دقيقة", completed: false },
      { id: 4, title: "التعامل مع DOM", duration: "90 دقيقة", completed: false },
      { id: 5, title: "الأحداث والتفاعل", duration: "80 دقيقة", completed: false }
    ]
  },
  {
    id: 2,
    title: "تطوير تطبيقات React",
    description: "بناء تطبيقات ويب تفاعلية باستخدام React.js",
    instructor: "فاطمة أحمد",
    duration: "12 أسبوع",
    level: "متوسط",
    price: 499,
    rating: 4.9,
    students: 890,
    image: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400",
    category: "Frontend",
    tags: ["React", "JavaScript", "Frontend"],
    lessons: [
      { id: 1, title: "مقدمة في React", duration: "50 دقيقة", completed: false },
      { id: 2, title: "المكونات والخصائص", duration: "70 دقيقة", completed: false },
      { id: 3, title: "إدارة الحالة", duration: "85 دقيقة", completed: false },
      { id: 4, title: "التوجيه والملاحة", duration: "65 دقيقة", completed: false }
    ]
  },
  {
    id: 3,
    title: "Python للمبتدئين",
    description: "تعلم لغة Python وتطبيقاتها في مختلف المجالات",
    instructor: "محمد علي",
    duration: "10 أسابيع",
    level: "مبتدئ",
    price: 399,
    rating: 4.7,
    students: 2100,
    image: "https://images.unsplash.com/photo-1526379095098-d400fd0bf935?w=400",
    category: "Backend",
    tags: ["Python", "Programming", "Data Science"],
    lessons: [
      { id: 1, title: "أساسيات Python", duration: "55 دقيقة", completed: false },
      { id: 2, title: "البنى التحكمية", duration: "65 دقيقة", completed: false },
      { id: 3, title: "الدوال والوحدات", duration: "70 دقيقة", completed: false }
    ]
  },
  {
    id: 4,
    title: "تصميم قواعد البيانات",
    description: "تعلم تصميم وإدارة قواعد البيانات باستخدام SQL",
    instructor: "سارة خالد",
    duration: "6 أسابيع",
    level: "متوسط",
    price: 349,
    rating: 4.6,
    students: 750,
    image: "https://images.unsplash.com/photo-1544383835-bda2bc66a55d?w=400",
    category: "Database",
    tags: ["SQL", "Database", "Backend"],
    lessons: [
      { id: 1, title: "مقدمة في قواعد البيانات", duration: "40 دقيقة", completed: false },
      { id: 2, title: "تصميم الجداول", duration: "60 دقيقة", completed: false }
    ]
  },
  {
    id: 5,
    title: "تطوير تطبيقات الموبايل",
    description: "بناء تطبيقات الهاتف المحمول باستخدام React Native",
    instructor: "عمر حسن",
    duration: "14 أسبوع",
    level: "متقدم",
    price: 699,
    rating: 4.8,
    students: 450,
    image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400",
    category: "Mobile",
    tags: ["React Native", "Mobile", "JavaScript"],
    lessons: [
      { id: 1, title: "إعداد البيئة", duration: "45 دقيقة", completed: false },
      { id: 2, title: "المكونات الأساسية", duration: "75 دقيقة", completed: false }
    ]
  }
];

export const categories = [
  { id: 1, name: "Frontend", count: 15, icon: "🎨" },
  { id: 2, name: "Backend", count: 12, icon: "⚙️" },
  { id: 3, name: "Mobile", count: 8, icon: "📱" },
  { id: 4, name: "Database", count: 6, icon: "🗄️" },
  { id: 5, name: "DevOps", count: 4, icon: "🚀" },
  { id: 6, name: "AI/ML", count: 7, icon: "🤖" }
];
