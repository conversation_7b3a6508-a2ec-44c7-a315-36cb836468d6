export const projects = [
  {
    id: 1,
    title: "متجر إلكتروني تفاعلي",
    description: "متجر إلكتروني كامل مع سلة التسوق ونظام الدفع",
    author: "أحمد محمد",
    authorAvatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100",
    image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600",
    technologies: ["React", "Node.js", "MongoDB"],
    likes: 245,
    views: 1200,
    comments: 18,
    createdAt: "2024-05-15",
    category: "Frontend",
    difficulty: "متوسط",
    githubUrl: "https://github.com/example/ecommerce",
    liveUrl: "https://example-store.netlify.app",
    featured: true,
    tags: ["E-commerce", "React", "Full-stack"]
  },
  {
    id: 2,
    title: "تطبيق إدارة المهام",
    description: "تطبيق لإدارة المهام اليومية مع إمكانية التعاون",
    author: "فاطمة أحمد",
    authorAvatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100",
    image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600",
    technologies: ["Vue.js", "Firebase", "Tailwind"],
    likes: 189,
    views: 890,
    comments: 12,
    createdAt: "2024-05-10",
    category: "Frontend",
    difficulty: "مبتدئ",
    githubUrl: "https://github.com/example/task-manager",
    liveUrl: "https://task-manager-demo.vercel.app",
    featured: false,
    tags: ["Productivity", "Vue.js", "Firebase"]
  },
  {
    id: 3,
    title: "API لنظام المدونات",
    description: "واجهة برمجية كاملة لإدارة المدونات والمقالات",
    author: "محمد علي",
    authorAvatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100",
    image: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=600",
    technologies: ["Node.js", "Express", "PostgreSQL"],
    likes: 156,
    views: 650,
    comments: 8,
    createdAt: "2024-05-08",
    category: "Backend",
    difficulty: "متقدم",
    githubUrl: "https://github.com/example/blog-api",
    liveUrl: "https://blog-api-demo.herokuapp.com",
    featured: true,
    tags: ["API", "Node.js", "Database"]
  },
  {
    id: 4,
    title: "تطبيق الطقس",
    description: "تطبيق موبايل لعرض حالة الطقس مع التنبؤات",
    author: "سارة خالد",
    authorAvatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100",
    image: "https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=600",
    technologies: ["React Native", "API Integration"],
    likes: 203,
    views: 1100,
    comments: 15,
    createdAt: "2024-05-05",
    category: "Mobile",
    difficulty: "مبتدئ",
    githubUrl: "https://github.com/example/weather-app",
    liveUrl: null,
    featured: false,
    tags: ["Weather", "React Native", "API"]
  },
  {
    id: 5,
    title: "لوحة تحكم تحليلية",
    description: "لوحة تحكم لعرض البيانات والإحصائيات التفاعلية",
    author: "عمر حسن",
    authorAvatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100",
    image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600",
    technologies: ["React", "D3.js", "Python"],
    likes: 178,
    views: 780,
    comments: 10,
    createdAt: "2024-05-02",
    category: "Data Science",
    difficulty: "متقدم",
    githubUrl: "https://github.com/example/analytics-dashboard",
    liveUrl: "https://analytics-demo.netlify.app",
    featured: true,
    tags: ["Analytics", "Data Visualization", "Dashboard"]
  },
  {
    id: 6,
    title: "منصة التعلم الإلكتروني",
    description: "منصة كاملة للتعلم الإلكتروني مع نظام الاختبارات",
    author: "ليلى أحمد",
    authorAvatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=100",
    image: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600",
    technologies: ["Next.js", "Prisma", "PostgreSQL"],
    likes: 267,
    views: 1450,
    comments: 22,
    createdAt: "2024-04-28",
    category: "Full-stack",
    difficulty: "متقدم",
    githubUrl: "https://github.com/example/learning-platform",
    liveUrl: "https://learning-platform-demo.vercel.app",
    featured: true,
    tags: ["Education", "Next.js", "Full-stack"]
  }
];

export const projectCategories = [
  { id: 1, name: "Frontend", count: 25, color: "#3B82F6" },
  { id: 2, name: "Backend", count: 18, color: "#10B981" },
  { id: 3, name: "Mobile", count: 12, color: "#8B5CF6" },
  { id: 4, name: "Full-stack", count: 15, color: "#F59E0B" },
  { id: 5, name: "Data Science", count: 8, color: "#EF4444" },
  { id: 6, name: "DevOps", count: 6, color: "#6B7280" }
];
