import React from 'react';
import { Link } from 'react-router-dom';
import { 
  Code, 
  Mail, 
  Phone, 
  MapPin, 
  Facebook, 
  Twitter, 
  Instagram, 
  Linkedin,
  Youtube,
  Heart
} from 'lucide-react';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    courses: [
      { name: 'JavaScript', href: '/courses?category=Frontend' },
      { name: 'React', href: '/courses?category=Frontend' },
      { name: 'Python', href: '/courses?category=Backend' },
      { name: 'Node.js', href: '/courses?category=Backend' },
      { name: 'React Native', href: '/courses?category=Mobile' }
    ],
    company: [
      { name: 'من نحن', href: '/about' },
      { name: 'فريق العمل', href: '/team' },
      { name: 'الوظائف', href: '/careers' },
      { name: 'الأخبار', href: '/news' },
      { name: 'اتصل بنا', href: '/contact' }
    ],
    support: [
      { name: 'مركز المساعدة', href: '/help' },
      { name: 'الأسئلة الشائعة', href: '/faq' },
      { name: 'الدعم الفني', href: '/support' },
      { name: 'تقرير مشكلة', href: '/report' },
      { name: 'طلب ميزة', href: '/feature-request' }
    ],
    legal: [
      { name: 'سياسة الخصوصية', href: '/privacy' },
      { name: 'شروط الاستخدام', href: '/terms' },
      { name: 'سياسة الاسترداد', href: '/refund' },
      { name: 'سياسة ملفات تعريف الارتباط', href: '/cookies' }
    ]
  };

  const socialLinks = [
    { name: 'Facebook', icon: Facebook, href: '#', color: 'hover:text-blue-600' },
    { name: 'Twitter', icon: Twitter, href: '#', color: 'hover:text-blue-400' },
    { name: 'Instagram', icon: Instagram, href: '#', color: 'hover:text-pink-600' },
    { name: 'LinkedIn', icon: Linkedin, href: '#', color: 'hover:text-blue-700' },
    { name: 'YouTube', icon: Youtube, href: '#', color: 'hover:text-red-600' }
  ];

  return (
    <footer className="bg-gray-900 text-white">
      {/* Newsletter Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-4">ابق على اطلاع بآخر التحديثات</h2>
            <p className="text-blue-100 text-lg mb-8">
              اشترك في نشرتنا الإخبارية لتحصل على أحدث الكورسات والعروض الخاصة
            </p>
            <div className="max-w-md mx-auto flex">
              <input
                type="email"
                placeholder="أدخل بريدك الإلكتروني"
                className="flex-1 px-4 py-3 rounded-r-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-white"
              />
              <button className="px-6 py-3 bg-white text-blue-600 rounded-l-lg hover:bg-gray-100 transition-colors font-semibold">
                اشتراك
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
            {/* Company Info */}
            <div className="lg:col-span-2">
              <Link to="/" className="flex items-center space-x-2 space-x-reverse mb-6">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <Code className="w-6 h-6 text-white" />
                </div>
                <span className="text-xl font-bold">أكاديمية البرمجة</span>
              </Link>
              
              <p className="text-gray-300 mb-6 leading-relaxed">
                منصة تعليمية رائدة في الوطن العربي لتعلم البرمجة وتطوير المهارات التقنية. 
                نوفر كورسات عالية الجودة ومجتمع داعم لمساعدتك في تحقيق أهدافك المهنية.
              </p>

              {/* Contact Info */}
              <div className="space-y-3">
                <div className="flex items-center space-x-3 space-x-reverse text-gray-300">
                  <Mail className="w-5 h-5 text-blue-400" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-3 space-x-reverse text-gray-300">
                  <Phone className="w-5 h-5 text-blue-400" />
                  <span>+966 50 123 4567</span>
                </div>
                <div className="flex items-center space-x-3 space-x-reverse text-gray-300">
                  <MapPin className="w-5 h-5 text-blue-400" />
                  <span>الرياض، المملكة العربية السعودية</span>
                </div>
              </div>
            </div>

            {/* Courses */}
            <div>
              <h3 className="text-lg font-semibold mb-6">الكورسات الشائعة</h3>
              <ul className="space-y-3">
                {footerLinks.courses.map((link) => (
                  <li key={link.name}>
                    <Link
                      to={link.href}
                      className="text-gray-300 hover:text-white transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Company */}
            <div>
              <h3 className="text-lg font-semibold mb-6">الشركة</h3>
              <ul className="space-y-3">
                {footerLinks.company.map((link) => (
                  <li key={link.name}>
                    <Link
                      to={link.href}
                      className="text-gray-300 hover:text-white transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Support */}
            <div>
              <h3 className="text-lg font-semibold mb-6">الدعم</h3>
              <ul className="space-y-3">
                {footerLinks.support.map((link) => (
                  <li key={link.name}>
                    <Link
                      to={link.href}
                      className="text-gray-300 hover:text-white transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Stats Section */}
          <div className="mt-16 pt-8 border-t border-gray-800">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold text-blue-400 mb-2">50+</div>
                <div className="text-gray-300">كورس متاح</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-green-400 mb-2">10K+</div>
                <div className="text-gray-300">طالب نشط</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-purple-400 mb-2">500+</div>
                <div className="text-gray-300">مشروع مشارك</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-yellow-400 mb-2">2K+</div>
                <div className="text-gray-300">شهادة صادرة</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-800 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            {/* Copyright */}
            <div className="flex items-center space-x-2 space-x-reverse text-gray-300 mb-4 md:mb-0">
              <span>© {currentYear} أكاديمية البرمجة. جميع الحقوق محفوظة.</span>
              <span className="flex items-center space-x-1 space-x-reverse">
                <span>صنع بـ</span>
                <Heart className="w-4 h-4 text-red-500 fill-current" />
                <span>في السعودية</span>
              </span>
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-4 space-x-reverse">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.href}
                  className={`text-gray-400 ${social.color} transition-colors`}
                  aria-label={social.name}
                >
                  <social.icon className="w-5 h-5" />
                </a>
              ))}
            </div>

            {/* Legal Links */}
            <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-400 mt-4 md:mt-0">
              {footerLinks.legal.slice(0, 2).map((link) => (
                <Link
                  key={link.name}
                  to={link.href}
                  className="hover:text-white transition-colors"
                >
                  {link.name}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
