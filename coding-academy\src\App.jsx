import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AppProvider } from './context/AppContext';
import Header from './components/Header';
import Footer from './components/Footer';
import Home from './pages/Home';
import Courses from './pages/Courses';
import Projects from './pages/Projects';
import CourseDetail from './pages/CourseDetail';
import Dashboard from './components/Dashboard';

function App() {
  return (
    <AppProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Header />
          <main>
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/courses" element={<Courses />} />
              <Route path="/projects" element={<Projects />} />
              <Route path="/community" element={<div className="p-8 text-center"><h1 className="text-2xl font-bold">صفحة المجتمع قيد التطوير</h1></div>} />
              <Route path="/profile" element={<div className="p-8 text-center"><h1 className="text-2xl font-bold">الملف الشخصي قيد التطوير</h1></div>} />
              <Route path="/achievements" element={<div className="p-8 text-center"><h1 className="text-2xl font-bold">صفحة الإنجازات قيد التطوير</h1></div>} />
              <Route path="/settings" element={<div className="p-8 text-center"><h1 className="text-2xl font-bold">صفحة الإعدادات قيد التطوير</h1></div>} />
              <Route path="/login" element={<div className="p-8 text-center"><h1 className="text-2xl font-bold">صفحة تسجيل الدخول قيد التطوير</h1></div>} />
              <Route path="/register" element={<div className="p-8 text-center"><h1 className="text-2xl font-bold">صفحة إنشاء الحساب قيد التطوير</h1></div>} />
              <Route path="/courses/:id" element={<CourseDetail />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/projects/:id" element={<div className="p-8 text-center"><h1 className="text-2xl font-bold">صفحة تفاصيل المشروع قيد التطوير</h1></div>} />
              <Route path="/projects/new" element={<div className="p-8 text-center"><h1 className="text-2xl font-bold">صفحة إضافة مشروع قيد التطوير</h1></div>} />
            </Routes>
          </main>
          <Footer />
        </div>
      </Router>
    </AppProvider>
  );
}

export default App;
