import React, { useState, useEffect, useRef } from 'react';
import { Search, X, Clock, BookOpen, Code, User, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { courses } from '../data/courses';
import { projects } from '../data/projects';

export default function SearchModal({ isOpen, onClose }) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState({ courses: [], projects: [], users: [] });
  const [activeTab, setActiveTab] = useState('all');
  const [recentSearches, setRecentSearches] = useState([
    'JavaScript أساسيات',
    'React تطوير',
    'Python للمبتدئين',
    'مشاريع الويب'
  ]);
  
  const inputRef = useRef(null);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  useEffect(() => {
    if (query.length > 0) {
      performSearch(query);
    } else {
      setResults({ courses: [], projects: [], users: [] });
    }
  }, [query]);

  const performSearch = (searchQuery) => {
    const lowerQuery = searchQuery.toLowerCase();
    
    // البحث في الكورسات
    const courseResults = courses.filter(course =>
      course.title.toLowerCase().includes(lowerQuery) ||
      course.description.toLowerCase().includes(lowerQuery) ||
      course.instructor.toLowerCase().includes(lowerQuery) ||
      course.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    ).slice(0, 5);

    // البحث في المشاريع
    const projectResults = projects.filter(project =>
      project.title.toLowerCase().includes(lowerQuery) ||
      project.description.toLowerCase().includes(lowerQuery) ||
      project.author.toLowerCase().includes(lowerQuery) ||
      project.technologies.some(tech => tech.toLowerCase().includes(lowerQuery))
    ).slice(0, 5);

    // محاكاة البحث في المستخدمين
    const userResults = [
      { id: 1, name: 'أحمد محمد', role: 'مطور Frontend', avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100' },
      { id: 2, name: 'فاطمة أحمد', role: 'مطورة Full-stack', avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100' }
    ].filter(user => 
      user.name.toLowerCase().includes(lowerQuery) ||
      user.role.toLowerCase().includes(lowerQuery)
    ).slice(0, 3);

    setResults({
      courses: courseResults,
      projects: projectResults,
      users: userResults
    });
  };

  const handleSearch = (searchTerm) => {
    setQuery(searchTerm);
    // إضافة إلى البحثات الأخيرة
    if (searchTerm && !recentSearches.includes(searchTerm)) {
      setRecentSearches(prev => [searchTerm, ...prev.slice(0, 4)]);
    }
  };

  const clearSearch = () => {
    setQuery('');
    setResults({ courses: [], projects: [], users: [] });
  };

  const totalResults = results.courses.length + results.projects.length + results.users.length;

  const tabs = [
    { id: 'all', label: 'الكل', count: totalResults },
    { id: 'courses', label: 'الكورسات', count: results.courses.length },
    { id: 'projects', label: 'المشاريع', count: results.projects.length },
    { id: 'users', label: 'المستخدمون', count: results.users.length }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="flex min-h-full items-start justify-center p-4 pt-16">
        <div className="relative w-full max-w-2xl bg-white rounded-lg shadow-xl">
          {/* Search Header */}
          <div className="border-b border-gray-200 p-4">
            <div className="flex items-center space-x-3 space-x-reverse">
              <Search className="w-5 h-5 text-gray-400" />
              <input
                ref={inputRef}
                type="text"
                placeholder="ابحث عن الكورسات، المشاريع، أو المستخدمين..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="flex-1 text-lg border-none outline-none placeholder-gray-400"
              />
              {query && (
                <button
                  onClick={clearSearch}
                  className="p-1 text-gray-400 hover:text-gray-600"
                >
                  <X className="w-4 h-4" />
                </button>
              )}
              <button
                onClick={onClose}
                className="p-1 text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="max-h-96 overflow-y-auto">
            {query === '' ? (
              // Recent Searches
              <div className="p-4">
                <h3 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                  <Clock className="w-4 h-4 ml-2" />
                  البحثات الأخيرة
                </h3>
                <div className="space-y-2">
                  {recentSearches.map((search, index) => (
                    <button
                      key={index}
                      onClick={() => handleSearch(search)}
                      className="flex items-center justify-between w-full p-2 text-right hover:bg-gray-50 rounded-lg transition-colors"
                    >
                      <span className="text-gray-700">{search}</span>
                      <ArrowRight className="w-4 h-4 text-gray-400" />
                    </button>
                  ))}
                </div>
              </div>
            ) : totalResults === 0 ? (
              // No Results
              <div className="p-8 text-center">
                <Search className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد نتائج</h3>
                <p className="text-gray-600">جرب البحث بكلمات مختلفة</p>
              </div>
            ) : (
              // Search Results
              <div>
                {/* Tabs */}
                <div className="border-b border-gray-200 px-4">
                  <nav className="flex space-x-8 space-x-reverse">
                    {tabs.map((tab) => (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`py-3 px-1 border-b-2 font-medium text-sm ${
                          activeTab === tab.id
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        {tab.label} ({tab.count})
                      </button>
                    ))}
                  </nav>
                </div>

                {/* Results */}
                <div className="p-4 space-y-4">
                  {/* Courses */}
                  {(activeTab === 'all' || activeTab === 'courses') && results.courses.length > 0 && (
                    <div>
                      {activeTab === 'all' && (
                        <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
                          <BookOpen className="w-4 h-4 ml-2" />
                          الكورسات
                        </h4>
                      )}
                      <div className="space-y-2">
                        {results.courses.map((course) => (
                          <Link
                            key={course.id}
                            to={`/courses/${course.id}`}
                            onClick={onClose}
                            className="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors"
                          >
                            <img
                              src={course.image}
                              alt={course.title}
                              className="w-12 h-12 rounded-lg object-cover"
                            />
                            <div className="mr-3 flex-1">
                              <h5 className="font-medium text-gray-900">{course.title}</h5>
                              <p className="text-sm text-gray-600">{course.instructor}</p>
                              <div className="flex items-center space-x-2 space-x-reverse text-xs text-gray-500">
                                <span>{course.level}</span>
                                <span>•</span>
                                <span>{course.duration}</span>
                              </div>
                            </div>
                            <ArrowRight className="w-4 h-4 text-gray-400" />
                          </Link>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Projects */}
                  {(activeTab === 'all' || activeTab === 'projects') && results.projects.length > 0 && (
                    <div>
                      {activeTab === 'all' && (
                        <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
                          <Code className="w-4 h-4 ml-2" />
                          المشاريع
                        </h4>
                      )}
                      <div className="space-y-2">
                        {results.projects.map((project) => (
                          <Link
                            key={project.id}
                            to={`/projects/${project.id}`}
                            onClick={onClose}
                            className="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors"
                          >
                            <img
                              src={project.image}
                              alt={project.title}
                              className="w-12 h-12 rounded-lg object-cover"
                            />
                            <div className="mr-3 flex-1">
                              <h5 className="font-medium text-gray-900">{project.title}</h5>
                              <p className="text-sm text-gray-600">{project.author}</p>
                              <div className="flex items-center space-x-2 space-x-reverse text-xs text-gray-500">
                                <span>{project.category}</span>
                                <span>•</span>
                                <span>{project.difficulty}</span>
                              </div>
                            </div>
                            <ArrowRight className="w-4 h-4 text-gray-400" />
                          </Link>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Users */}
                  {(activeTab === 'all' || activeTab === 'users') && results.users.length > 0 && (
                    <div>
                      {activeTab === 'all' && (
                        <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
                          <User className="w-4 h-4 ml-2" />
                          المستخدمون
                        </h4>
                      )}
                      <div className="space-y-2">
                        {results.users.map((user) => (
                          <Link
                            key={user.id}
                            to={`/profile/${user.id}`}
                            onClick={onClose}
                            className="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors"
                          >
                            <img
                              src={user.avatar}
                              alt={user.name}
                              className="w-12 h-12 rounded-full object-cover"
                            />
                            <div className="mr-3 flex-1">
                              <h5 className="font-medium text-gray-900">{user.name}</h5>
                              <p className="text-sm text-gray-600">{user.role}</p>
                            </div>
                            <ArrowRight className="w-4 h-4 text-gray-400" />
                          </Link>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          {query && totalResults > 0 && (
            <div className="border-t border-gray-200 p-4">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>عرض {totalResults} نتيجة</span>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <span>اضغط</span>
                  <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Enter</kbd>
                  <span>للبحث المتقدم</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
