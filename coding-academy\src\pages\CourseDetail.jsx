import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { 
  Play, 
  Clock, 
  Users, 
  Star, 
  BookOpen, 
  CheckCircle,
  Lock,
  Download,
  Share2,
  Heart,
  MessageCircle,
  ArrowRight,
  ArrowLeft
} from 'lucide-react';
import { courses } from '../data/courses';
import { useApp } from '../context/AppContext';
import CodeEditor from '../components/CodeEditor';

export default function CourseDetail() {
  const { id } = useParams();
  const { enrolledCourses, enrollInCourse, completedLessons, completeLesson } = useApp();
  const [activeTab, setActiveTab] = useState('overview');
  const [currentLesson, setCurrentLesson] = useState(0);

  const course = courses.find(c => c.id === parseInt(id));
  const isEnrolled = enrolledCourses.some(c => c.id === course?.id);

  if (!course) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">الكورس غير موجود</h1>
          <Link to="/courses" className="text-blue-600 hover:text-blue-700">
            العودة إلى الكورسات
          </Link>
        </div>
      </div>
    );
  }

  const isLessonCompleted = (lessonId) => {
    return completedLessons.some(cl => cl.lessonId === lessonId && cl.courseId === course.id);
  };

  const handleEnroll = () => {
    if (!isEnrolled) {
      enrollInCourse(course);
    }
  };

  const handleCompleteLesson = (lessonId) => {
    if (!isLessonCompleted(lessonId)) {
      completeLesson(lessonId, course.id);
    }
  };

  const tabs = [
    { id: 'overview', label: 'نظرة عامة', icon: BookOpen },
    { id: 'curriculum', label: 'المنهج', icon: Play },
    { id: 'reviews', label: 'التقييمات', icon: Star },
    { id: 'discussion', label: 'النقاش', icon: MessageCircle }
  ];

  const sampleCode = `// مثال على JavaScript
function greetStudent(name) {
  return \`مرحباً \${name}! أهلاً بك في كورس \${course.title}\`;
}

// جرب تشغيل الكود
console.log(greetStudent("أحمد"));

// تمرين: أنشئ دالة لحساب مجموع رقمين
function sum(a, b) {
  // اكتب الكود هنا
  return a + b;
}

console.log(sum(5, 3)); // يجب أن تطبع 8`;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Course Info */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-2 space-x-reverse mb-4">
                <span className="px-3 py-1 bg-white/20 rounded-full text-sm">{course.category}</span>
                <span className="px-3 py-1 bg-white/20 rounded-full text-sm">{course.level}</span>
              </div>
              
              <h1 className="text-4xl font-bold mb-4">{course.title}</h1>
              <p className="text-xl text-blue-100 mb-6">{course.description}</p>
              
              <div className="flex items-center space-x-6 space-x-reverse mb-6">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <img
                    src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100"
                    alt={course.instructor}
                    className="w-10 h-10 rounded-full"
                  />
                  <span>{course.instructor}</span>
                </div>
                <div className="flex items-center space-x-1 space-x-reverse">
                  <Star className="w-5 h-5 text-yellow-400 fill-current" />
                  <span>{course.rating}</span>
                  <span className="text-blue-200">({course.students} طالب)</span>
                </div>
                <div className="flex items-center space-x-1 space-x-reverse">
                  <Clock className="w-5 h-5" />
                  <span>{course.duration}</span>
                </div>
              </div>

              <div className="flex items-center space-x-4 space-x-reverse">
                {isEnrolled ? (
                  <button className="flex items-center space-x-2 space-x-reverse px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <CheckCircle className="w-5 h-5" />
                    <span>مسجل في الكورس</span>
                  </button>
                ) : (
                  <button
                    onClick={handleEnroll}
                    className="flex items-center space-x-2 space-x-reverse px-6 py-3 bg-white text-blue-600 rounded-lg hover:bg-gray-100 transition-colors font-semibold"
                  >
                    <BookOpen className="w-5 h-5" />
                    <span>التسجيل في الكورس</span>
                  </button>
                )}
                <button className="p-3 bg-white/20 rounded-lg hover:bg-white/30 transition-colors">
                  <Heart className="w-5 h-5" />
                </button>
                <button className="p-3 bg-white/20 rounded-lg hover:bg-white/30 transition-colors">
                  <Share2 className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Course Preview */}
            <div className="lg:col-span-1">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
                <img
                  src={course.image}
                  alt={course.title}
                  className="w-full h-48 object-cover rounded-lg mb-4"
                />
                <div className="text-center">
                  <div className="text-3xl font-bold mb-2">{course.price} ريال</div>
                  <div className="text-blue-200 text-sm mb-4">دفعة واحدة - وصول مدى الحياة</div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center justify-between">
                      <span>عدد الدروس:</span>
                      <span>{course.lessons.length} درس</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>المدة:</span>
                      <span>{course.duration}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>المستوى:</span>
                      <span>{course.level}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>الشهادة:</span>
                      <span>متاحة</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="flex space-x-8 space-x-reverse">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 space-x-reverse py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            {activeTab === 'overview' && (
              <div className="space-y-8">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">ماذا ستتعلم؟</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[
                      'أساسيات اللغة والمفاهيم الأساسية',
                      'التعامل مع البيانات والمتغيرات',
                      'بناء التطبيقات التفاعلية',
                      'أفضل الممارسات في البرمجة',
                      'حل المشاكل البرمجية',
                      'بناء مشاريع حقيقية'
                    ].map((item, index) => (
                      <div key={index} className="flex items-center space-x-2 space-x-reverse">
                        <CheckCircle className="w-5 h-5 text-green-500" />
                        <span className="text-gray-700">{item}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">وصف الكورس</h2>
                  <div className="prose prose-lg text-gray-700">
                    <p>
                      هذا الكورس مصمم خصيصاً للمبتدئين الذين يريدون تعلم البرمجة من الصفر. 
                      سنبدأ بالمفاهيم الأساسية وننتقل تدريجياً إلى المواضيع المتقدمة.
                    </p>
                    <p>
                      ستتعلم من خلال أمثلة عملية ومشاريع حقيقية تساعدك على فهم المفاهيم 
                      وتطبيقها في الواقع. كما ستحصل على دعم مستمر من المدرب والمجتمع.
                    </p>
                  </div>
                </div>

                {/* Interactive Code Example */}
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">جرب الكود بنفسك</h2>
                  <CodeEditor 
                    initialCode={sampleCode}
                    language="javascript"
                    readOnly={!isEnrolled}
                  />
                  {!isEnrolled && (
                    <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <p className="text-yellow-800">
                        سجل في الكورس لتتمكن من تعديل وتشغيل الكود
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'curriculum' && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">منهج الكورس</h2>
                <div className="space-y-4">
                  {course.lessons.map((lesson, index) => (
                    <div
                      key={lesson.id}
                      className={`border rounded-lg p-4 ${
                        isEnrolled ? 'hover:bg-gray-50 cursor-pointer' : 'opacity-60'
                      }`}
                      onClick={() => isEnrolled && setCurrentLesson(index)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          {isEnrolled ? (
                            isLessonCompleted(lesson.id) ? (
                              <CheckCircle className="w-6 h-6 text-green-500" />
                            ) : (
                              <Play className="w-6 h-6 text-blue-500" />
                            )
                          ) : (
                            <Lock className="w-6 h-6 text-gray-400" />
                          )}
                          <div>
                            <h3 className="font-medium text-gray-900">
                              {index + 1}. {lesson.title}
                            </h3>
                            <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
                              <Clock className="w-4 h-4" />
                              <span>{lesson.duration}</span>
                            </div>
                          </div>
                        </div>
                        {isEnrolled && !isLessonCompleted(lesson.id) && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleCompleteLesson(lesson.id);
                            }}
                            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                          >
                            إكمال
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'reviews' && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">تقييمات الطلاب</h2>
                <div className="space-y-6">
                  {[
                    {
                      name: 'أحمد محمد',
                      rating: 5,
                      comment: 'كورس ممتاز ومفيد جداً. المدرب يشرح بطريقة واضحة ومفهومة.',
                      date: '2024-05-15'
                    },
                    {
                      name: 'فاطمة أحمد',
                      rating: 4,
                      comment: 'استفدت كثيراً من هذا الكورس. المحتوى منظم بشكل جيد.',
                      date: '2024-05-10'
                    }
                  ].map((review, index) => (
                    <div key={index} className="border-b border-gray-200 pb-6">
                      <div className="flex items-center space-x-3 space-x-reverse mb-3">
                        <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium">
                          {review.name.charAt(0)}
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{review.name}</div>
                          <div className="flex items-center space-x-1 space-x-reverse">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`w-4 h-4 ${
                                  i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                                }`}
                              />
                            ))}
                            <span className="text-sm text-gray-500 mr-2">{review.date}</span>
                          </div>
                        </div>
                      </div>
                      <p className="text-gray-700">{review.comment}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'discussion' && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">منتدى النقاش</h2>
                <div className="text-center py-12">
                  <MessageCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">منتدى النقاش قيد التطوير</h3>
                  <p className="text-gray-600">سيتم إضافة هذه الميزة قريباً</p>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">تقدمك في الكورس</h3>
              
              {isEnrolled ? (
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600">الدروس المكتملة</span>
                      <span className="text-sm font-medium">
                        {course.lessons.filter(l => isLessonCompleted(l.id)).length} / {course.lessons.length}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full transition-all duration-300"
                        style={{ 
                          width: `${(course.lessons.filter(l => isLessonCompleted(l.id)).length / course.lessons.length) * 100}%` 
                        }}
                      ></div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <button className="w-full flex items-center justify-center space-x-2 space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                      <Play className="w-4 h-4" />
                      <span>متابعة التعلم</span>
                    </button>
                    <button className="w-full flex items-center justify-center space-x-2 space-x-reverse px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                      <Download className="w-4 h-4" />
                      <span>تحميل المواد</span>
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-center">
                  <Lock className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                  <p className="text-gray-600 mb-4">سجل في الكورس لتتبع تقدمك</p>
                  <button
                    onClick={handleEnroll}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    التسجيل الآن
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
