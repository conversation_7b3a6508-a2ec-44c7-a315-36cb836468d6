import React from 'react';
import { Link } from 'react-router-dom';
import { 
  ArrowLeft, 
  BookOpen, 
  Users, 
  Trophy, 
  Code, 
  Star,
  Play,
  TrendingUp,
  Award,
  Zap
} from 'lucide-react';
import CourseCard from '../components/CourseCard';
import ProjectCard from '../components/ProjectCard';
import { courses } from '../data/courses';
import { projects } from '../data/projects';

export default function Home() {
  const featuredCourses = courses.slice(0, 3);
  const featuredProjects = projects.filter(p => p.featured).slice(0, 3);

  const stats = [
    { icon: BookOpen, label: 'كورس متاح', value: '50+', color: 'text-blue-600' },
    { icon: Users, label: 'طالب نشط', value: '10K+', color: 'text-green-600' },
    { icon: Code, label: 'مشروع مشارك', value: '500+', color: 'text-purple-600' },
    { icon: Trophy, label: 'شهادة صادرة', value: '2K+', color: 'text-yellow-600' }
  ];

  const features = [
    {
      icon: BookOpen,
      title: 'كورسات شاملة',
      description: 'تعلم من خلال كورسات مصممة بعناية تغطي جميع جوانب البرمجة',
      color: 'bg-blue-50 text-blue-600'
    },
    {
      icon: Code,
      title: 'مشاريع عملية',
      description: 'طبق ما تعلمته من خلال مشاريع حقيقية وشاركها مع المجتمع',
      color: 'bg-green-50 text-green-600'
    },
    {
      icon: Users,
      title: 'مجتمع تفاعلي',
      description: 'تواصل مع المطورين الآخرين وتبادل الخبرات والمعرفة',
      color: 'bg-purple-50 text-purple-600'
    },
    {
      icon: Trophy,
      title: 'شهادات معتمدة',
      description: 'احصل على شهادات معتمدة تثبت مهاراتك في البرمجة',
      color: 'bg-yellow-50 text-yellow-600'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800"></div>
        <div className="absolute inset-0 bg-black/20"></div>
        
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse-slow"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse-slow"></div>
        </div>

        <div className="relative max-w-7xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 animate-fade-in">
            تعلم البرمجة
            <span className="block text-yellow-300">وابني مستقبلك</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto animate-slide-up">
            انضم إلى أكبر منصة تعليمية للبرمجة في الوطن العربي. تعلم، طبق، شارك، وتطور مع مجتمع من المطورين المحترفين
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-bounce-in">
            <Link
              to="/courses"
              className="flex items-center space-x-2 space-x-reverse px-8 py-4 bg-white text-blue-600 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
            >
              <BookOpen className="w-5 h-5" />
              <span>ابدأ التعلم الآن</span>
            </Link>
            <Link
              to="/projects"
              className="flex items-center space-x-2 space-x-reverse px-8 py-4 border-2 border-white text-white rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300"
            >
              <Code className="w-5 h-5" />
              <span>استكشف المشاريع</span>
            </Link>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16">
            {stats.map((stat, index) => (
              <div key={index} className="text-center animate-fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg bg-white/20 ${stat.color} mb-2`}>
                  <stat.icon className="w-6 h-6" />
                </div>
                <div className="text-2xl font-bold text-white">{stat.value}</div>
                <div className="text-gray-200 text-sm">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              لماذا تختار أكاديمية البرمجة؟
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              نوفر لك كل ما تحتاجه لتصبح مطور محترف من خلال منهج شامل ومجتمع داعم
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-white rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2"
              >
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${feature.color} mb-4`}>
                  <feature.icon className="w-6 h-6" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Courses */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-12">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">الكورسات المميزة</h2>
              <p className="text-gray-600">ابدأ رحلتك التعليمية مع أفضل الكورسات</p>
            </div>
            <Link
              to="/courses"
              className="flex items-center space-x-2 space-x-reverse text-blue-600 hover:text-blue-700 font-medium"
            >
              <span>عرض جميع الكورسات</span>
              <ArrowLeft className="w-4 h-4" />
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredCourses.map((course) => (
              <CourseCard key={course.id} course={course} />
            ))}
          </div>
        </div>
      </section>

      {/* Featured Projects */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-12">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-2">المشاريع المميزة</h2>
              <p className="text-gray-600">استلهم من مشاريع المطورين الآخرين</p>
            </div>
            <Link
              to="/projects"
              className="flex items-center space-x-2 space-x-reverse text-blue-600 hover:text-blue-700 font-medium"
            >
              <span>عرض جميع المشاريع</span>
              <ArrowLeft className="w-4 h-4" />
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredProjects.map((project) => (
              <ProjectCard key={project.id} project={project} />
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            هل أنت مستعد لبدء رحلتك؟
          </h2>
          <p className="text-xl text-gray-200 mb-8">
            انضم إلى آلاف المطورين الذين بدأوا رحلتهم معنا وحققوا أهدافهم المهنية
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/register"
              className="flex items-center space-x-2 space-x-reverse px-8 py-4 bg-white text-blue-600 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              <Zap className="w-5 h-5" />
              <span>إنشاء حساب مجاني</span>
            </Link>
            <Link
              to="/courses"
              className="flex items-center space-x-2 space-x-reverse px-8 py-4 border-2 border-white text-white rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
            >
              <Play className="w-5 h-5" />
              <span>تصفح الكورسات</span>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
