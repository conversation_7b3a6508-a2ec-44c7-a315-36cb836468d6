import React from 'react';
import { TrendingUp, TrendingDown } from 'lucide-react';

export default function StatsCard({ 
  title, 
  value, 
  change, 
  changeType = 'increase', 
  icon: Icon, 
  color = 'blue',
  description 
}) {
  const colorClasses = {
    blue: {
      bg: 'bg-blue-50',
      text: 'text-blue-600',
      icon: 'text-blue-500'
    },
    green: {
      bg: 'bg-green-50',
      text: 'text-green-600',
      icon: 'text-green-500'
    },
    purple: {
      bg: 'bg-purple-50',
      text: 'text-purple-600',
      icon: 'text-purple-500'
    },
    yellow: {
      bg: 'bg-yellow-50',
      text: 'text-yellow-600',
      icon: 'text-yellow-500'
    },
    red: {
      bg: 'bg-red-50',
      text: 'text-red-600',
      icon: 'text-red-500'
    }
  };

  const colors = colorClasses[color] || colorClasses.blue;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-600">{title}</h3>
            {Icon && (
              <div className={`p-2 rounded-lg ${colors.bg}`}>
                <Icon className={`w-5 h-5 ${colors.icon}`} />
              </div>
            )}
          </div>
          
          <div className="flex items-baseline space-x-2 space-x-reverse">
            <span className="text-2xl font-bold text-gray-900">{value}</span>
            {change && (
              <div className={`flex items-center space-x-1 space-x-reverse text-sm ${
                changeType === 'increase' ? 'text-green-600' : 'text-red-600'
              }`}>
                {changeType === 'increase' ? (
                  <TrendingUp className="w-4 h-4" />
                ) : (
                  <TrendingDown className="w-4 h-4" />
                )}
                <span>{change}</span>
              </div>
            )}
          </div>
          
          {description && (
            <p className="text-sm text-gray-500 mt-1">{description}</p>
          )}
        </div>
      </div>
    </div>
  );
}

// مكون للإحصائيات المبسطة
export function SimpleStatsCard({ title, value, icon: Icon, color = 'blue' }) {
  const colorClasses = {
    blue: 'text-blue-600',
    green: 'text-green-600',
    purple: 'text-purple-600',
    yellow: 'text-yellow-600',
    red: 'text-red-600'
  };

  return (
    <div className="text-center">
      {Icon && (
        <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg bg-white/20 ${colorClasses[color]} mb-2`}>
          <Icon className="w-6 h-6" />
        </div>
      )}
      <div className="text-2xl font-bold text-white">{value}</div>
      <div className="text-gray-200 text-sm">{title}</div>
    </div>
  );
}

// مكون للإحصائيات مع رسم بياني بسيط
export function ChartStatsCard({ title, value, data, color = 'blue' }) {
  const maxValue = Math.max(...data);
  
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-600">{title}</h3>
        <span className="text-2xl font-bold text-gray-900">{value}</span>
      </div>
      
      {/* Simple Bar Chart */}
      <div className="flex items-end space-x-1 space-x-reverse h-16">
        {data.map((item, index) => (
          <div
            key={index}
            className={`flex-1 bg-${color}-200 rounded-t`}
            style={{
              height: `${(item / maxValue) * 100}%`,
              minHeight: '4px'
            }}
          />
        ))}
      </div>
      
      <div className="flex justify-between text-xs text-gray-500 mt-2">
        <span>الأسبوع الماضي</span>
        <span>اليوم</span>
      </div>
    </div>
  );
}

// مكون للإحصائيات مع دائرة التقدم
export function ProgressStatsCard({ title, value, percentage, color = 'blue' }) {
  const circumference = 2 * Math.PI * 20;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  const colorClasses = {
    blue: 'stroke-blue-500',
    green: 'stroke-green-500',
    purple: 'stroke-purple-500',
    yellow: 'stroke-yellow-500',
    red: 'stroke-red-500'
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium text-gray-600 mb-1">{title}</h3>
          <span className="text-2xl font-bold text-gray-900">{value}</span>
          <div className="text-sm text-gray-500">{percentage}% مكتمل</div>
        </div>
        
        <div className="relative w-16 h-16">
          <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 50 50">
            <circle
              cx="25"
              cy="25"
              r="20"
              stroke="currentColor"
              strokeWidth="4"
              fill="none"
              className="text-gray-200"
            />
            <circle
              cx="25"
              cy="25"
              r="20"
              stroke="currentColor"
              strokeWidth="4"
              fill="none"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              className={`${colorClasses[color]} transition-all duration-300`}
              strokeLinecap="round"
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-sm font-semibold text-gray-900">{percentage}%</span>
          </div>
        </div>
      </div>
    </div>
  );
}
