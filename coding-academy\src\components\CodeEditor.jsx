import React, { useState } from 'react';
import { Play, Copy, Download, RotateCcw, Maximize2 } from 'lucide-react';

export default function CodeEditor({ 
  initialCode = '', 
  language = 'javascript',
  readOnly = false,
  showOutput = true 
}) {
  const [code, setCode] = useState(initialCode);
  const [output, setOutput] = useState('');
  const [isRunning, setIsRunning] = useState(false);

  const runCode = () => {
    setIsRunning(true);
    setOutput('جاري تشغيل الكود...');
    
    // محاكاة تشغيل الكود
    setTimeout(() => {
      try {
        if (language === 'javascript') {
          // محاكاة بسيطة لتشغيل JavaScript
          const result = eval(code);
          setOutput(result !== undefined ? String(result) : 'تم تشغيل الكود بنجاح');
        } else {
          setOutput('تم تشغيل الكود بنجاح');
        }
      } catch (error) {
        setOutput(`خطأ: ${error.message}`);
      }
      setIsRunning(false);
    }, 1000);
  };

  const copyCode = () => {
    navigator.clipboard.writeText(code);
    // يمكن إضافة إشعار هنا
  };

  const resetCode = () => {
    setCode(initialCode);
    setOutput('');
  };

  const downloadCode = () => {
    const element = document.createElement('a');
    const file = new Blob([code], { type: 'text/plain' });
    element.href = URL.createObjectURL(file);
    element.download = `code.${language === 'javascript' ? 'js' : language}`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
  };

  const getLanguageLabel = (lang) => {
    const labels = {
      javascript: 'JavaScript',
      python: 'Python',
      html: 'HTML',
      css: 'CSS',
      react: 'React'
    };
    return labels[lang] || lang;
  };

  return (
    <div className="bg-gray-900 rounded-lg overflow-hidden shadow-lg">
      {/* Header */}
      <div className="bg-gray-800 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-4 space-x-reverse">
          <div className="flex space-x-2 space-x-reverse">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          </div>
          <span className="text-gray-300 text-sm font-medium">
            {getLanguageLabel(language)}
          </span>
        </div>
        
        <div className="flex items-center space-x-2 space-x-reverse">
          {!readOnly && (
            <>
              <button
                onClick={runCode}
                disabled={isRunning}
                className="flex items-center space-x-1 space-x-reverse px-3 py-1.5 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:opacity-50 transition-colors"
              >
                <Play className="w-4 h-4" />
                <span>{isRunning ? 'جاري التشغيل...' : 'تشغيل'}</span>
              </button>
              <button
                onClick={resetCode}
                className="p-1.5 text-gray-400 hover:text-white transition-colors"
                title="إعادة تعيين"
              >
                <RotateCcw className="w-4 h-4" />
              </button>
            </>
          )}
          <button
            onClick={copyCode}
            className="p-1.5 text-gray-400 hover:text-white transition-colors"
            title="نسخ الكود"
          >
            <Copy className="w-4 h-4" />
          </button>
          <button
            onClick={downloadCode}
            className="p-1.5 text-gray-400 hover:text-white transition-colors"
            title="تحميل الكود"
          >
            <Download className="w-4 h-4" />
          </button>
          <button className="p-1.5 text-gray-400 hover:text-white transition-colors">
            <Maximize2 className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Code Area */}
      <div className="relative">
        <textarea
          value={code}
          onChange={(e) => setCode(e.target.value)}
          readOnly={readOnly}
          className="w-full h-64 bg-gray-900 text-gray-100 p-4 font-mono text-sm resize-none focus:outline-none code-editor"
          placeholder="اكتب الكود هنا..."
          style={{
            lineHeight: '1.5',
            tabSize: 2
          }}
        />
        
        {/* Line Numbers */}
        <div className="absolute top-0 right-0 p-4 text-gray-500 text-sm font-mono pointer-events-none select-none">
          {code.split('\n').map((_, index) => (
            <div key={index} className="h-6 leading-6">
              {index + 1}
            </div>
          ))}
        </div>
      </div>

      {/* Output */}
      {showOutput && (
        <div className="border-t border-gray-700">
          <div className="bg-gray-800 px-4 py-2">
            <span className="text-gray-300 text-sm font-medium">النتيجة:</span>
          </div>
          <div className="bg-gray-900 p-4 min-h-[80px]">
            <pre className="text-gray-100 text-sm font-mono whitespace-pre-wrap">
              {output || 'اضغط على "تشغيل" لرؤية النتيجة'}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}

// مكون مبسط لعرض الكود فقط
export function CodeBlock({ code, language = 'javascript' }) {
  return (
    <div className="bg-gray-900 rounded-lg overflow-hidden">
      <div className="bg-gray-800 px-4 py-2">
        <span className="text-gray-300 text-sm font-medium">
          {language.toUpperCase()}
        </span>
      </div>
      <div className="p-4">
        <pre className="text-gray-100 text-sm font-mono overflow-x-auto">
          <code>{code}</code>
        </pre>
      </div>
    </div>
  );
}
