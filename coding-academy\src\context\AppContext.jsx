import React, { createContext, useContext, useReducer, useEffect } from 'react';

const AppContext = createContext();

const initialState = {
  user: {
    id: 1,
    name: 'أحمد محمد',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
    joinDate: '2024-01-15'
  },
  enrolledCourses: [],
  completedLessons: [],
  userProjects: [],
  notifications: [
    {
      id: 1,
      type: 'info',
      title: 'مرحباً بك!',
      message: 'أهلاً بك في أكاديمية البرمجة. ابدأ رحلتك التعليمية الآن!',
      read: false,
      timestamp: new Date().toISOString()
    }
  ],
  theme: 'light',
  language: 'ar',
  achievements: [],
  points: 150,
  level: 2
};

function appReducer(state, action) {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, user: action.payload };

    case 'ENROLL_COURSE':
      return {
        ...state,
        enrolledCourses: [...state.enrolledCourses, action.payload]
      };

    case 'COMPLETE_LESSON':
      const newCompletedLessons = [...state.completedLessons, action.payload];
      const newPoints = state.points + 10; // 10 points per lesson
      const newLevel = Math.floor(newPoints / 100) + 1;

      return {
        ...state,
        completedLessons: newCompletedLessons,
        points: newPoints,
        level: newLevel
      };

    case 'ADD_PROJECT':
      return {
        ...state,
        userProjects: [...state.userProjects, action.payload],
        points: state.points + 50 // 50 points for project submission
      };

    case 'LIKE_PROJECT':
      return {
        ...state,
        userProjects: state.userProjects.map(project =>
          project.id === action.payload
            ? { ...project, likes: project.likes + 1 }
            : project
        )
      };

    case 'ADD_NOTIFICATION':
      return {
        ...state,
        notifications: [action.payload, ...state.notifications]
      };

    case 'MARK_NOTIFICATION_READ':
      return {
        ...state,
        notifications: state.notifications.map(notif =>
          notif.id === action.payload
            ? { ...notif, read: true }
            : notif
        )
      };

    case 'TOGGLE_THEME':
      return {
        ...state,
        theme: state.theme === 'light' ? 'dark' : 'light'
      };

    case 'SET_LANGUAGE':
      return {
        ...state,
        language: action.payload
      };

    case 'ADD_ACHIEVEMENT':
      return {
        ...state,
        achievements: [...state.achievements, action.payload],
        points: state.points + action.payload.points
      };

    case 'LOAD_STATE':
      return { ...state, ...action.payload };

    default:
      return state;
  }
}

export function AppProvider({ children }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Load state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem('codingAcademyState');
    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState);
        dispatch({ type: 'LOAD_STATE', payload: parsedState });
      } catch (error) {
        console.error('Error loading saved state:', error);
      }
    }
  }, []);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('codingAcademyState', JSON.stringify(state));
  }, [state]);

  const enrollInCourse = (course) => {
    dispatch({ type: 'ENROLL_COURSE', payload: course });
    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        type: 'success',
        title: 'تم التسجيل بنجاح',
        message: `تم تسجيلك في كورس ${course.title}`,
        read: false,
        timestamp: new Date().toISOString()
      }
    });
  };

  const completeLesson = (lessonId, courseId) => {
    dispatch({ type: 'COMPLETE_LESSON', payload: { lessonId, courseId } });

    // Check for achievements
    const completedCount = state.completedLessons.length + 1;
    if (completedCount === 1) {
      dispatch({
        type: 'ADD_ACHIEVEMENT',
        payload: {
          id: 'first_lesson',
          title: 'الدرس الأول',
          description: 'أكملت أول درس لك',
          icon: '🎯',
          points: 25
        }
      });
    }
  };

  const submitProject = (project) => {
    const newProject = {
      ...project,
      id: Date.now(),
      author: state.user?.name || 'مستخدم مجهول',
      authorAvatar: state.user?.avatar || '',
      likes: 0,
      views: 0,
      comments: 0,
      createdAt: new Date().toISOString().split('T')[0]
    };

    dispatch({ type: 'ADD_PROJECT', payload: newProject });
    dispatch({
      type: 'ADD_NOTIFICATION',
      payload: {
        id: Date.now(),
        type: 'success',
        title: 'تم رفع المشروع',
        message: `تم رفع مشروع ${project.title} بنجاح`,
        read: false,
        timestamp: new Date().toISOString()
      }
    });
  };

  const value = {
    ...state,
    dispatch,
    enrollInCourse,
    completeLesson,
    submitProject
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
