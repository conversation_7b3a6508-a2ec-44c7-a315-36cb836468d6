import React from 'react';
import { 
  BookOpen, 
  Code, 
  Trophy, 
  Clock, 
  TrendingUp, 
  Target,
  Calendar,
  Star,
  Users,
  Award
} from 'lucide-react';
import { useApp } from '../context/AppContext';
import CourseCard from './CourseCard';
import ProjectCard from './ProjectCard';

export default function Dashboard() {
  const { 
    user, 
    enrolledCourses, 
    completedLessons, 
    userProjects, 
    points, 
    level, 
    achievements 
  } = useApp();

  // حساب الإحصائيات
  const totalLessons = enrolledCourses.reduce((total, course) => total + course.lessons.length, 0);
  const completedLessonsCount = completedLessons.length;
  const progressPercentage = totalLessons > 0 ? (completedLessonsCount / totalLessons) * 100 : 0;
  const pointsToNextLevel = (level * 100) - points;

  const stats = [
    {
      icon: BookOpen,
      label: 'الكورسات المسجلة',
      value: enrolledCourses.length,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      icon: Target,
      label: 'الدروس المكتملة',
      value: completedLessonsCount,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      icon: Code,
      label: 'المشاريع المشاركة',
      value: userProjects.length,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      icon: Trophy,
      label: 'النقاط المكتسبة',
      value: points,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50'
    }
  ];

  const recentAchievements = achievements.slice(0, 3);
  const recentCourses = enrolledCourses.slice(0, 3);
  const recentProjects = userProjects.slice(0, 3);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-8 mb-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">
                مرحباً، {user?.name || 'المطور'}! 👋
              </h1>
              <p className="text-blue-100 text-lg">
                استمر في رحلتك التعليمية وحقق أهدافك
              </p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold mb-1">المستوى {level}</div>
              <div className="text-blue-200 text-sm">
                {pointsToNextLevel > 0 ? `${pointsToNextLevel} نقطة للمستوى التالي` : 'تهانينا!'}
              </div>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mt-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-blue-100">التقدم الإجمالي</span>
              <span className="text-sm text-blue-100">{Math.round(progressPercentage)}%</span>
            </div>
            <div className="w-full bg-blue-500/30 rounded-full h-2">
              <div 
                className="bg-white h-2 rounded-full transition-all duration-500 progress-bar"
                style={{ '--progress-width': `${progressPercentage}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <div className="flex items-center">
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`w-6 h-6 ${stat.color}`} />
                </div>
                <div className="mr-4">
                  <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                  <div className="text-sm text-gray-600">{stat.label}</div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Achievements */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <Award className="w-5 h-5 text-yellow-500 ml-2" />
                الإنجازات الأخيرة
              </h2>
            </div>
            
            {recentAchievements.length === 0 ? (
              <div className="text-center py-8">
                <Trophy className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500">لا توجد إنجازات بعد</p>
                <p className="text-sm text-gray-400">أكمل الدروس لكسب إنجازات جديدة</p>
              </div>
            ) : (
              <div className="space-y-3">
                {recentAchievements.map((achievement) => (
                  <div key={achievement.id} className="flex items-center p-3 bg-yellow-50 rounded-lg">
                    <div className="text-2xl ml-3">{achievement.icon}</div>
                    <div>
                      <div className="font-medium text-gray-900">{achievement.title}</div>
                      <div className="text-sm text-gray-600">{achievement.description}</div>
                      <div className="text-xs text-yellow-600">+{achievement.points} نقطة</div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Learning Activity */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <TrendingUp className="w-5 h-5 text-green-500 ml-2" />
                نشاط التعلم
              </h2>
            </div>

            {/* Weekly Progress */}
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">هذا الأسبوع</span>
                  <span className="text-sm font-medium text-gray-900">
                    {Math.floor(Math.random() * 10) + 5} ساعات
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '70%' }}></div>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">الشهر الماضي</span>
                  <span className="text-sm font-medium text-gray-900">
                    {Math.floor(Math.random() * 40) + 20} ساعة
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: '85%' }}></div>
                </div>
              </div>

              {/* Streak */}
              <div className="bg-orange-50 rounded-lg p-3">
                <div className="flex items-center">
                  <div className="text-2xl ml-2">🔥</div>
                  <div>
                    <div className="font-medium text-gray-900">
                      {Math.floor(Math.random() * 15) + 1} يوم متتالي
                    </div>
                    <div className="text-sm text-gray-600">استمر في التعلم!</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <Clock className="w-5 h-5 text-blue-500 ml-2" />
                إجراءات سريعة
              </h2>
            </div>

            <div className="space-y-3">
              <button className="w-full flex items-center justify-center space-x-2 space-x-reverse px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <BookOpen className="w-4 h-4" />
                <span>متابعة التعلم</span>
              </button>
              
              <button className="w-full flex items-center justify-center space-x-2 space-x-reverse px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <Code className="w-4 h-4" />
                <span>إضافة مشروع جديد</span>
              </button>
              
              <button className="w-full flex items-center justify-center space-x-2 space-x-reverse px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                <Users className="w-4 h-4" />
                <span>انضم للمجتمع</span>
              </button>
            </div>

            {/* Next Lesson */}
            {enrolledCourses.length > 0 && (
              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <div className="text-sm font-medium text-blue-900 mb-1">الدرس التالي</div>
                <div className="text-sm text-blue-700">
                  {enrolledCourses[0].lessons[0]?.title || 'لا توجد دروس متاحة'}
                </div>
                <div className="text-xs text-blue-600 mt-1">
                  في كورس {enrolledCourses[0].title}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Recent Courses */}
        {recentCourses.length > 0 && (
          <div className="mt-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">الكورسات الحالية</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recentCourses.map((course) => (
                <CourseCard key={course.id} course={course} variant="compact" />
              ))}
            </div>
          </div>
        )}

        {/* Recent Projects */}
        {recentProjects.length > 0 && (
          <div className="mt-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">مشاريعي الأخيرة</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recentProjects.map((project) => (
                <ProjectCard key={project.id} project={project} variant="compact" />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
