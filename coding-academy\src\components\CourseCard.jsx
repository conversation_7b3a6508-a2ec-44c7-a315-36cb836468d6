import React from 'react';
import { Link } from 'react-router-dom';
import { 
  <PERSON>, 
  <PERSON>, 
  Star, 
  BookOpen, 
  Award,
  Play,
  Heart,
  Share2
} from 'lucide-react';
import { useApp } from '../context/AppContext';

export default function CourseCard({ course, variant = 'default' }) {
  const { enrolledCourses, enrollInCourse } = useApp();
  const isEnrolled = enrolledCourses.some(c => c.id === course.id);

  const handleEnroll = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isEnrolled) {
      enrollInCourse(course);
    }
  };

  const getLevelColor = (level) => {
    switch (level) {
      case 'مبتدئ': return 'bg-green-100 text-green-800';
      case 'متوسط': return 'bg-yellow-100 text-yellow-800';
      case 'متقدم': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (variant === 'compact') {
    return (
      <Link to={`/courses/${course.id}`} className="block group">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300 overflow-hidden">
          <div className="flex">
            <div className="w-24 h-24 flex-shrink-0">
              <img
                src={course.image}
                alt={course.title}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1 p-4">
              <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-1">
                {course.title}
              </h3>
              <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                {course.description}
              </p>
              <div className="flex items-center justify-between mt-2">
                <span className="text-sm text-gray-500">{course.instructor}</span>
                <div className="flex items-center space-x-1 space-x-reverse">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-600">{course.rating}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Link>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300 overflow-hidden group">
      {/* Course Image */}
      <div className="relative overflow-hidden">
        <img
          src={course.image}
          alt={course.title}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div className="absolute top-4 right-4">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(course.level)}`}>
            {course.level}
          </span>
        </div>
        <div className="absolute top-4 left-4 flex space-x-2 space-x-reverse">
          <button className="p-2 bg-white/80 backdrop-blur-sm rounded-full hover:bg-white transition-colors">
            <Heart className="w-4 h-4 text-gray-600" />
          </button>
          <button className="p-2 bg-white/80 backdrop-blur-sm rounded-full hover:bg-white transition-colors">
            <Share2 className="w-4 h-4 text-gray-600" />
          </button>
        </div>
        {isEnrolled && (
          <div className="absolute bottom-4 right-4">
            <div className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 space-x-reverse">
              <Award className="w-3 h-3" />
              <span>مسجل</span>
            </div>
          </div>
        )}
      </div>

      {/* Course Content */}
      <div className="p-6">
        {/* Category */}
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-blue-600 font-medium">{course.category}</span>
          <div className="flex items-center space-x-1 space-x-reverse text-sm text-gray-500">
            <Clock className="w-4 h-4" />
            <span>{course.duration}</span>
          </div>
        </div>

        {/* Title */}
        <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
          <Link to={`/courses/${course.id}`}>{course.title}</Link>
        </h3>

        {/* Description */}
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
          {course.description}
        </p>

        {/* Instructor */}
        <div className="flex items-center mb-4">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
            {course.instructor.charAt(0)}
          </div>
          <span className="mr-2 text-sm text-gray-700">{course.instructor}</span>
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between mb-4 text-sm text-gray-600">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="flex items-center space-x-1 space-x-reverse">
              <Star className="w-4 h-4 text-yellow-400 fill-current" />
              <span>{course.rating}</span>
            </div>
            <div className="flex items-center space-x-1 space-x-reverse">
              <Users className="w-4 h-4" />
              <span>{course.students.toLocaleString()}</span>
            </div>
            <div className="flex items-center space-x-1 space-x-reverse">
              <BookOpen className="w-4 h-4" />
              <span>{course.lessons.length} درس</span>
            </div>
          </div>
        </div>

        {/* Tags */}
        <div className="flex flex-wrap gap-2 mb-4">
          {course.tags.slice(0, 3).map((tag, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
            >
              {tag}
            </span>
          ))}
        </div>

        {/* Price and Action */}
        <div className="flex items-center justify-between">
          <div className="text-right">
            <span className="text-2xl font-bold text-gray-900">{course.price}</span>
            <span className="text-sm text-gray-600 mr-1">ريال</span>
          </div>
          
          {isEnrolled ? (
            <Link
              to={`/courses/${course.id}/learn`}
              className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <Play className="w-4 h-4" />
              <span>متابعة التعلم</span>
            </Link>
          ) : (
            <button
              onClick={handleEnroll}
              className="flex items-center space-x-2 space-x-reverse px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <BookOpen className="w-4 h-4" />
              <span>التسجيل الآن</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
